# Padrão de UX para Páginas de Listagem

Este documento descreve o novo padrão de UX implementado para páginas de listagem, especificamente aplicado à página de clientes.

## 🎯 Objetivos do Novo Padrão

- **Interface limpa e focada**: Remover elementos desnecessários que poluem a interface
- **Filtros organizados**: Usar Sheet lateral para filtros avançados
- **Ações claras**: Botões de ação bem definidos e acessíveis
- **Métricas visuais**: Cards de métricas importantes em destaque
- **Reutilização**: Padrão que pode ser aplicado em outras páginas

## 🏗️ Estrutura dos Componentes

### 1. CustomerMetrics
- **Propósito**: Exibir métricas principais em cards visuais
- **Localização**: Topo da página, abaixo do header
- **Conteúdo**: Total de clientes, verificados, ativos, receita total

### 2. CustomerFiltersSheet
- **Propósito**: Filtros avançados em Sheet lateral
- **Funcionalidades**:
  - Busca por texto
  - Filtros de status (verificados, ativos, pendentes, inativos)
  - Filtros de segmento (VIP, ativos, novos, inativos)
  - Filtros de data (período de cadastro)
  - Filtros de receita (faixa de valores)
- **Acesso**: Botão "Filtros" com contador de filtros ativos

### 3. AddCustomerModal
- **Propósito**: Modal para adicionar novo cliente
- **Funcionalidades**:
  - Formulário completo com validação
  - Campos obrigatórios e opcionais
  - Seleção de estado brasileiro
  - Observações e informações adicionais

### 4. CustomersList
- **Propósito**: Lista principal de clientes
- **Funcionalidades**:
  - Tabela responsiva com informações essenciais
  - Ações por cliente (visualizar, editar, excluir)
  - Resumo dos filtros ativos
  - Contador de resultados filtrados

## 🔧 Como Implementar em Outras Páginas

### 1. Estrutura Básica
```tsx
export default function MinhaPagina() {
  return (
    <div className="space-y-6">
      <PageHeader title="Título" subtitle="Subtítulo" />

      {/* Cards de métricas */}
      <MinhasMetricas />

      {/* Lista principal */}
      <MinhaLista />
    </div>
  );
}
```

### 2. Componente de Filtros
```tsx
export function MeusFiltrosSheet({ onFiltersChange, activeFilters }) {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm">
          <FilterIcon className="h-4 w-4 mr-2" />
          Filtros
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent>
        {/* Conteúdo dos filtros */}
      </SheetContent>
    </Sheet>
  );
}
```

### 3. Modal de Adição
```tsx
export function AddItemModal({ onItemAdd }) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>
          <PlusIcon className="h-4 w-4 mr-2" />
          Novo Item
        </Button>
      </DialogTrigger>
      <DialogContent>
        {/* Formulário */}
      </DialogContent>
    </Dialog>
  );
}
```

## 📱 Responsividade

- **Mobile-first**: Design responsivo com breakpoints do Tailwind
- **Sheet**: Largura adaptativa (400px mobile, 540px desktop)
- **Grid**: Métricas em grid responsivo (1 coluna mobile, 4 colunas desktop)
- **Tabela**: Scroll horizontal em dispositivos pequenos

## 🎨 Design System

### Cores
- **Verde**: Status positivo, verificados, receita
- **Azul**: Status ativo, informações neutras
- **Amarelo**: Status pendente, alertas
- **Vermelho**: Status inativo, ações destrutivas
- **Cinza**: Status neutro, informações secundárias

### Ícones
- **Lucide React**: Biblioteca de ícones consistente
- **Tamanhos**: h-4 w-4 para botões, h-5 w-5 para títulos
- **Cores**: Seguem o sistema de cores do status

### Tipografia
- **Títulos**: text-2xl font-bold para métricas
- **Subtítulos**: text-sm text-muted-foreground para descrições
- **Labels**: text-sm font-medium para campos de formulário

## 🚀 Próximos Passos

1. **Aplicar padrão** em outras páginas (produtos, vendas, etc.)
2. **Criar componentes genéricos** para filtros e modais
3. **Implementar validação** com react-hook-form e zod
4. **Adicionar testes** para os novos componentes
5. **Documentar padrões** de acessibilidade e SEO

## 📚 Dependências

- `@radix-ui/react-dialog` - Modal e Sheet
- `@radix-ui/react-separator` - Separadores visuais
- `@radix-ui/react-checkbox` - Checkboxes para filtros
- `@radix-ui/react-select` - Seleção de estados
- `lucide-react` - Ícones
- `tailwind-merge` - Utilitários de classes CSS
