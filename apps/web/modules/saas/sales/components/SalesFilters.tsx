"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  FilterIcon,
  CalendarIcon,
  DollarSignIcon,
  ShoppingCartIcon
} from "lucide-react";

interface SalesFiltersProps {
  organizationId: string;
}

export function SalesFilters({ organizationId }: SalesFiltersProps) {
  const periods = [
    { id: "7d", name: "7 dias", count: 0 },
    { id: "30d", name: "30 dias", count: 0 },
    { id: "90d", name: "90 dias", count: 0 },
    { id: "1y", name: "1 ano", count: 0 },
    { id: "all", name: "Todo período", count: 0 }
  ];

  const statuses = [
    { id: "all", name: "Todos", count: 0 },
    { id: "approved", name: "Aprovadas", count: 0 },
    { id: "pending", name: "Pendent<PERSON>", count: 0 },
    { id: "cancelled", name: "<PERSON><PERSON><PERSON>s", count: 0 },
    { id: "refunded", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", count: 0 }
  ];

  const paymentMethods = [
    { id: "all", name: "Todos", count: 0 },
    { id: "credit_card", name: "Cartão de Crédito", count: 0 },
    { id: "pix", name: "PIX", count: 0 },
    { id: "bank_transfer", name: "Transferência", count: 0 }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FilterIcon className="h-5 w-5" />
          Filtros de Vendas
        </CardTitle>
        <CardDescription>
          Configure filtros para análise de vendas
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Filtros de Período */}
          <div>
            <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" />
              Período
            </h4>
            <div className="flex flex-wrap gap-2">
              {periods.map((period) => (
                <Button
                  key={period.id}
                  variant="outline"
                  size="sm"
                  className="h-8"
                >
                  {period.name}
                  <Badge variant="secondary" className="ml-2">
                    {period.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Filtros de Status */}
          <div>
            <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
              <ShoppingCartIcon className="h-4 w-4" />
              Status
            </h4>
            <div className="flex flex-wrap gap-2">
              {statuses.map((status) => (
                <Button
                  key={status.id}
                  variant="outline"
                  size="sm"
                  className="h-8"
                >
                  {status.name}
                  <Badge variant="secondary" className="ml-2">
                    {status.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Filtros de Método de Pagamento */}
          <div>
            <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
              <DollarSignIcon className="h-4 w-4" />
              Método de Pagamento
            </h4>
            <div className="flex flex-wrap gap-2">
              {paymentMethods.map((method) => (
                <Button
                  key={method.id}
                  variant="outline"
                  size="sm"
                  className="h-8"
                >
                  {method.name}
                  <Badge variant="secondary" className="ml-2">
                    {method.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Filtros Avançados */}
          <div className="pt-4 border-t">
            <Button variant="outline" size="sm">
              <FilterIcon className="h-4 w-4 mr-2" />
              Filtros Avançados
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
