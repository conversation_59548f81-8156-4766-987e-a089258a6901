"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  EyeIcon,
  EditIcon,
  DownloadIcon,
  MoreHorizontalIcon
} from "lucide-react";

interface SalesTableProps {
  organizationId: string;
}

interface Sale {
  id: string;
  products: string[];
  buyer: string;
  seller: string;
  saleDate: string;
  paymentMethod: string;
  amount: number;
  status: "approved" | "pending" | "cancelled" | "refunded";
}

// Dados mockados para demonstração
const mockSales: Sale[] = [];

export function SalesTable({ organizationId }: SalesTableProps) {
  const getStatusBadge = (status: Sale["status"]) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-100 text-green-800 border border-green-200">Aprovada</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200">Pendente</Badge>;
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800 border border-red-200">Cancelada</Badge>;
      case "refunded":
        return <Badge className="bg-blue-100 text-blue-800 border border-blue-200">Reembolsada</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200">Desconhecido</Badge>;
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method.toLowerCase()) {
      case "pix":
        return "💳";
      case "credit_card":
        return "💳";
      case "bank_transfer":
        return "🏦";
      default:
        return "💰";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Tabela de Vendas</CardTitle>
            <CardDescription>
              Lista detalhada de todas as vendas
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {mockSales.length > 0 ? (
          <div className="border rounded-lg">
            <div className="bg-muted/50 px-4 py-3 border-b">
              <div className="grid grid-cols-8 gap-4 text-sm font-medium">
                <div>ID</div>
                <div>Produto(s)</div>
                <div>Comprador</div>
                <div>Vendedor</div>
                <div>Data da venda</div>
                <div>Método</div>
                <div>Valor</div>
                <div>Status</div>
              </div>
            </div>

            <div className="divide-y">
              {mockSales.map((sale) => (
                <div key={sale.id} className="px-4 py-3 hover:bg-muted/30 transition-colors">
                  <div className="grid grid-cols-8 gap-4 items-center">
                    <div className="font-mono text-sm">{sale.id}</div>
                    <div className="text-sm">
                      {sale.products.join(", ")}
                    </div>
                    <div className="text-sm font-medium">{sale.buyer}</div>
                    <div className="text-sm font-medium">{sale.seller}</div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(sale.saleDate).toLocaleDateString('pt-BR')}
                    </div>
                    <div className="text-sm flex items-center gap-2">
                      <span>{getPaymentMethodIcon(sale.paymentMethod)}</span>
                      {sale.paymentMethod}
                    </div>
                    <div className="text-sm font-medium">
                      R$ {sale.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(sale.status)}
                      <Button variant="ghost" size="sm">
                        <MoreHorizontalIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="mx-auto w-24 h-24 bg-muted/50 rounded-full flex items-center justify-center mb-4">
              <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                <span className="text-2xl">📄</span>
              </div>
            </div>
            <h3 className="text-lg font-medium mb-2">Nenhuma venda encontrada</h3>
            <p className="text-muted-foreground">
              As vendas aparecerão aqui quando forem registradas.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
