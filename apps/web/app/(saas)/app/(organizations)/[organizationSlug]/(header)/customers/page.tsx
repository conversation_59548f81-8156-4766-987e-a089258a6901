import { CustomerMetrics } from "@/modules/saas/customers/components/CustomerMetrics";
import { CustomersList } from "@/modules/saas/customers/components/CustomersList";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { notFound } from "next/navigation";

export default async function CustomersPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Clientes"
        subtitle="Gerencie seus clientes e acompanhe métricas importantes"
      />

      <CustomerMetrics organizationId={organization.id} />
      <CustomersList organizationId={organization.id} />
    </div>
  );
}
