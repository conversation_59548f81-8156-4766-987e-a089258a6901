import { getActiveOrganization } from "@saas/auth/lib/server";
import { FinancialSummary } from "@saas/finance/components/FinancialSummary";
import { FinancialCharts } from "@saas/finance/components/FinancialCharts";
import { FinancialFilters } from "@saas/finance/components/FinancialFilters";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { notFound } from "next/navigation";

export default async function FinancePage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Financeiro"
        subtitle="Controle financeiro completo da sua organização"
      />


      <FinancialSummary organizationId={organization.id} />
      <FinancialCharts organizationId={organization.id} />
    </div>
  );
}
