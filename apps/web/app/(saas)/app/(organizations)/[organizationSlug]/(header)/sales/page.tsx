import { SalesAnalytics, SalesFilters, SalesTable } from "@/modules/saas/sales";
import { getActiveOrganization } from "@saas/auth/lib/server";

import { PageHeader } from "@saas/shared/components/PageHeader";
import { notFound } from "next/navigation";

export default async function SalesPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Vendas"
        subtitle="<PERSON><PERSON><PERSON><PERSON> todas as vendas da sua organização"
      />

      <SalesAnalytics organizationId={organization.id} />

      <div className="space-y-4">
        <SalesFilters organizationId={organization.id} />
        <SalesTable organizationId={organization.id} />
      </div>
    </div>
  );
}
